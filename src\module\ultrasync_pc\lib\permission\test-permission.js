/**
 * 权限管理器测试文件
 * 用于验证权限检查功能是否正常工作
 */

import permissionManager from './index.js';

/**
 * 测试权限管理器功能
 */
export function testPermissionManager() {
    console.log('开始测试权限管理器...');

    // 测试用户数据
    const testUsers = [
        { uid: 1, username: 'user1', role: 1, nickname: '普通用户' },
        { uid: 2, username: 'manager1', role: 2, nickname: '管理者' },
        { uid: 3, username: 'admin1', role: 3, nickname: '管理员' },
        { uid: 4, username: 'judge1', role: 4, nickname: '评委' },
        { uid: 5, username: 'superadmin1', role: 5, nickname: '超级管理员' }
    ];

    // 测试权限列表
    const testPermissions = [
        'backgroundManage',
        'userManage',
        'user_create',
        'user_edit',
        'user_delete',
        'hospitalManage',
        'examManage',
        'exam_review',
        'admin',
        'super_admin'
    ];

    // 对每个用户进行权限测试
    testUsers.forEach(user => {
        console.log(`\n测试用户: ${user.nickname} (角色: ${user.role})`);
        console.log('=' .repeat(50));

        // 初始化权限管理器
        permissionManager.initialize(user);

        // 测试每个权限
        testPermissions.forEach(permission => {
            const hasPermission = permissionManager.checkPermission(permission);
            console.log(`${permission}: ${hasPermission ? '✓' : '✗'}`);
        });

        // 测试角色检查方法
        console.log(`\n角色检查:`);
        console.log(`isAdmin(): ${permissionManager.isAdmin() ? '✓' : '✗'}`);
        console.log(`isSuperAdmin(): ${permissionManager.isSuperAdmin() ? '✓' : '✗'}`);
        console.log(`getUserRole(): ${permissionManager.getUserRole()}`);
    });

    console.log('\n权限管理器测试完成!');
}

/**
 * 测试批量权限检查
 */
export function testBatchPermissions() {
    console.log('\n开始测试批量权限检查...');

    // 使用管理员用户进行测试
    const adminUser = { uid: 3, username: 'admin1', role: 3, nickname: '管理员' };
    permissionManager.initialize(adminUser);

    const batchPermissions = {
        features: [
            { feature: 'backgroundManage', key: 'canAccessAdmin' },
            { feature: 'userManage', key: 'canManageUsers' },
            { feature: 'user_delete', key: 'canDeleteUsers' },
            { feature: 'hospitalManage', key: 'canManageHospitals' },
            { feature: 'super_admin', key: 'isSuperAdmin' }
        ]
    };

    const results = permissionManager.batchCheckPermissions(batchPermissions);
    
    console.log('批量权限检查结果:');
    Object.entries(results).forEach(([key, value]) => {
        console.log(`${key}: ${value ? '✓' : '✗'}`);
    });
}

/**
 * 测试权限更新
 */
export function testPermissionUpdate() {
    console.log('\n开始测试权限更新...');

    // 初始化为普通用户
    const user = { uid: 1, username: 'user1', role: 1, nickname: '普通用户' };
    permissionManager.initialize(user);

    console.log('初始权限状态:');
    console.log(`backgroundManage: ${permissionManager.checkPermission('backgroundManage') ? '✓' : '✗'}`);
    console.log(`isAdmin(): ${permissionManager.isAdmin() ? '✓' : '✗'}`);

    // 更新用户角色为管理员
    console.log('\n更新用户角色为管理员...');
    permissionManager.updateUserInfo({ role: 3 });

    console.log('更新后权限状态:');
    console.log(`backgroundManage: ${permissionManager.checkPermission('backgroundManage') ? '✓' : '✗'}`);
    console.log(`isAdmin(): ${permissionManager.isAdmin() ? '✓' : '✗'}`);
}

/**
 * 测试错误处理
 */
export function testErrorHandling() {
    console.log('\n开始测试错误处理...');

    try {
        // 测试未初始化时的权限检查
        const newManager = new (permissionManager.constructor)();
        newManager.checkPermission('backgroundManage');
    } catch (error) {
        console.log(`未初始化错误处理: ✓ (${error.message})`);
    }

    // 测试不存在的权限
    permissionManager.initialize({ uid: 1, role: 3 });
    const unknownPermission = permissionManager.checkPermission('unknownPermission');
    console.log(`未知权限处理: ${unknownPermission ? '✓' : '✗'} (默认通过功能权限检查)`);
}

/**
 * 性能测试
 */
export function testPerformance() {
    console.log('\n开始性能测试...');

    const user = { uid: 1, username: 'admin1', role: 3, nickname: '管理员' };
    permissionManager.initialize(user);

    const iterations = 10000;
    const startTime = performance.now();

    for (let i = 0; i < iterations; i++) {
        permissionManager.checkPermission('backgroundManage');
        permissionManager.checkPermission('userManage');
        permissionManager.checkPermission('admin');
    }

    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / (iterations * 3);

    console.log(`性能测试结果:`);
    console.log(`总计: ${iterations * 3} 次权限检查`);
    console.log(`总时间: ${totalTime.toFixed(2)} ms`);
    console.log(`平均时间: ${avgTime.toFixed(4)} ms/次`);
}

/**
 * 运行所有测试
 */
export function runAllTests() {
    console.log('🚀 开始运行权限管理器完整测试套件...\n');

    try {
        testPermissionManager();
        testBatchPermissions();
        testPermissionUpdate();
        testErrorHandling();
        testPerformance();

        console.log('\n✅ 所有测试完成!');
    } catch (error) {
        console.error('\n❌ 测试过程中出现错误:', error);
    }
}

// 如果在浏览器环境中，可以通过控制台运行测试
if (typeof window !== 'undefined') {
    window.testPermissionManager = {
        runAllTests,
        testPermissionManager,
        testBatchPermissions,
        testPermissionUpdate,
        testErrorHandling,
        testPerformance
    };

    console.log('权限管理器测试工具已加载到 window.testPermissionManager');
    console.log('运行 window.testPermissionManager.runAllTests() 开始测试');
}

/**
 * 权限矩阵展示
 */
export function showPermissionMatrix() {
    console.log('\n权限矩阵:');
    console.log('角色 | 普通用户 | 管理者 | 管理员 | 评委 | 超级管理员');
    console.log('-'.repeat(60));

    const permissions = [
        'backgroundManage',
        'userManage',
        'user_create',
        'user_edit',
        'user_delete',
        'hospitalManage',
        'examManage',
        'exam_review'
    ];

    const roles = [1, 2, 3, 4, 5];
    const roleNames = ['普通用户', '管理者', '管理员', '评委', '超级管理员'];

    permissions.forEach(permission => {
        let row = `${permission.padEnd(15)} |`;
        
        roles.forEach(role => {
            const testUser = { uid: 1, role };
            permissionManager.initialize(testUser);
            const hasPermission = permissionManager.checkPermission(permission);
            row += ` ${hasPermission ? '✓' : '✗'.padEnd(8)} |`;
        });
        
        console.log(row);
    });
}
