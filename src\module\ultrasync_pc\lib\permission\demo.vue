<template>
    <div class="permission-demo">
        <h2>权限管理器演示</h2>
        
        <!-- 用户信息显示 -->
        <el-card class="user-info">
            <div slot="header">
                <span>当前用户信息</span>
            </div>
            <p>用户ID: {{ userInfo.uid }}</p>
            <p>用户名: {{ userInfo.username }}</p>
            <p>角色: {{ userInfo.role }} ({{ getRoleName(userInfo.role) }})</p>
            <p>是否管理员: {{ $isAdmin() ? '是' : '否' }}</p>
            <p>是否超级管理员: {{ $isSuperAdmin() ? '是' : '否' }}</p>
        </el-card>

        <!-- 权限检查演示 -->
        <el-card class="permission-checks">
            <div slot="header">
                <span>权限检查演示</span>
            </div>
            
            <div class="permission-item">
                <span>后台管理权限:</span>
                <el-tag :type="$checkPermission('backgroundManage') ? 'success' : 'danger'">
                    {{ $checkPermission('backgroundManage') ? '有权限' : '无权限' }}
                </el-tag>
            </div>

            <div class="permission-item">
                <span>用户管理权限:</span>
                <el-tag :type="$checkPermission('userManage') ? 'success' : 'danger'">
                    {{ $checkPermission('userManage') ? '有权限' : '无权限' }}
                </el-tag>
            </div>

            <div class="permission-item">
                <span>删除用户权限:</span>
                <el-tag :type="$checkPermission('user_delete') ? 'success' : 'danger'">
                    {{ $checkPermission('user_delete') ? '有权限' : '无权限' }}
                </el-tag>
            </div>

            <div class="permission-item">
                <span>医院管理权限:</span>
                <el-tag :type="$checkPermission('hospitalManage') ? 'success' : 'danger'">
                    {{ $checkPermission('hospitalManage') ? '有权限' : '无权限' }}
                </el-tag>
            </div>

            <div class="permission-item">
                <span>考试审核权限:</span>
                <el-tag :type="$checkPermission('exam_review') ? 'success' : 'danger'">
                    {{ $checkPermission('exam_review') ? '有权限' : '无权限' }}
                </el-tag>
            </div>
        </el-card>

        <!-- 功能按钮演示 -->
        <el-card class="function-buttons">
            <div slot="header">
                <span>功能按钮演示</span>
            </div>
            
            <!-- 使用v-if控制显示 -->
            <el-button 
                v-if="$checkPermission('userManage')"
                type="primary" 
                @click="handleUserManage">
                用户管理
            </el-button>

            <el-button 
                v-if="$checkPermission('user_create')"
                type="success" 
                @click="handleCreateUser">
                创建用户
            </el-button>

            <el-button 
                v-if="$checkPermission('user_delete')"
                type="danger" 
                @click="handleDeleteUser">
                删除用户
            </el-button>

            <el-button 
                v-if="$checkPermission('hospitalManage')"
                type="info" 
                @click="handleHospitalManage">
                医院管理
            </el-button>

            <!-- 使用disabled控制禁用状态 -->
            <el-button 
                :disabled="!$checkPermission('dataExport')"
                @click="handleDataExport">
                数据导出
            </el-button>
        </el-card>

        <!-- 角色切换演示 -->
        <el-card class="role-switch">
            <div slot="header">
                <span>角色切换演示</span>
            </div>
            
            <el-select v-model="selectedRole" @change="switchRole" placeholder="选择角色">
                <el-option label="普通用户 (1)" :value="1"></el-option>
                <el-option label="管理者 (2)" :value="2"></el-option>
                <el-option label="管理员 (3)" :value="3"></el-option>
                <el-option label="评委 (4)" :value="4"></el-option>
                <el-option label="超级管理员 (5)" :value="5"></el-option>
            </el-select>
            
            <p class="tip">切换角色后可以看到权限变化</p>
        </el-card>

        <!-- 批量权限检查演示 -->
        <el-card class="batch-permissions">
            <div slot="header">
                <span>批量权限检查结果</span>
            </div>
            
            <div v-for="(value, key) in batchPermissionResults" :key="key" class="permission-item">
                <span>{{ key }}:</span>
                <el-tag :type="value ? 'success' : 'danger'">
                    {{ value ? '有权限' : '无权限' }}
                </el-tag>
            </div>
        </el-card>
    </div>
</template>

<script>
import permissionManager from './index.js';

export default {
    name: 'PermissionDemo',
    data() {
        return {
            selectedRole: 1,
            userInfo: {
                uid: 'demo_user',
                username: 'demo',
                role: 1
            },
            batchPermissionResults: {}
        };
    },
    
    mounted() {
        // 初始化权限管理器
        this.initPermissionManager();
        // 执行批量权限检查
        this.performBatchPermissionCheck();
    },

    methods: {
        // 初始化权限管理器
        initPermissionManager() {
            permissionManager.initialize(this.userInfo);
        },

        // 获取角色名称
        getRoleName(role) {
            const roleNames = {
                1: '普通用户',
                2: '管理者', 
                3: '管理员',
                4: '评委',
                5: '超级管理员'
            };
            return roleNames[role] || '未知角色';
        },

        // 切换角色
        switchRole(newRole) {
            this.userInfo.role = newRole;
            // 更新权限管理器中的用户信息
            permissionManager.updateUserInfo(this.userInfo);
            // 重新执行批量权限检查
            this.performBatchPermissionCheck();
            
            this.$message.success(`已切换到${this.getRoleName(newRole)}`);
        },

        // 执行批量权限检查
        performBatchPermissionCheck() {
            this.batchPermissionResults = permissionManager.batchCheckPermissions({
                features: [
                    { feature: 'backgroundManage', key: '后台管理' },
                    { feature: 'userManage', key: '用户管理' },
                    { feature: 'user_delete', key: '删除用户' },
                    { feature: 'hospitalManage', key: '医院管理' },
                    { feature: 'exam_review', key: '考试审核' },
                    { feature: 'admin', key: '管理员权限' },
                    { feature: 'super_admin', key: '超级管理员权限' }
                ]
            });
        },

        // 功能处理方法
        handleUserManage() {
            if (!this.$checkPermission('userManage')) {
                this.$message.error('您没有用户管理权限');
                return;
            }
            this.$message.success('打开用户管理页面');
        },

        handleCreateUser() {
            if (!this.$checkPermission('user_create')) {
                this.$message.error('您没有创建用户权限');
                return;
            }
            this.$message.success('打开创建用户对话框');
        },

        handleDeleteUser() {
            if (!this.$checkPermission('user_delete')) {
                this.$message.error('您没有删除用户权限');
                return;
            }
            this.$message.success('执行删除用户操作');
        },

        handleHospitalManage() {
            if (!this.$checkPermission('hospitalManage')) {
                this.$message.error('您没有医院管理权限');
                return;
            }
            this.$message.success('打开医院管理页面');
        },

        handleDataExport() {
            if (!this.$checkPermission('dataExport')) {
                this.$message.error('您没有数据导出权限');
                return;
            }
            this.$message.success('开始导出数据');
        }
    }
};
</script>

<style scoped>
.permission-demo {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.el-card {
    margin-bottom: 20px;
}

.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.permission-item span {
    font-weight: 500;
}

.function-buttons .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
}

.tip {
    margin-top: 10px;
    color: #666;
    font-size: 12px;
}

.user-info p {
    margin: 8px 0;
}
</style>
