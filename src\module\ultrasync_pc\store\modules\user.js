import Vue from 'vue'
import cloneDeep from 'lodash/cloneDeep'
const initState ={
    name: '',
    type: 1,
    organizationName:''
}
export default {
    state: cloneDeep(initState),
    mutations: {
        resetStore(state){
            if(window.vm.$store.state){
                Vue.set(window.vm.$store.state,'user',cloneDeep(initState))
            }
        },
        updateUser(state, valObj) {
            for (let key in valObj) {
                Vue.set(state,key,valObj[key]);
                // state[key] = valObj[key];
            }
            if (state.uid) {
                window.logReporter.setConfig({
                    uin:`${state.nickname}`
                })

                // 当用户信息更新且有uid时，初始化权限管理器
                if (window.vm && window.vm.initPermissionManagerFromStore) {
                    window.vm.initPermissionManagerFromStore(state);
                }
            }else{
                window.logReporter.setConfig({
                    uin:''
                })
            }
        },
    },
    actions: {},
    getters: {}
}
